import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product.dart';

class FirestoreService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all products from Firestore
  static Future<List<Product>> getAllProducts() async {
    try {
      final QuerySnapshot snapshot =
          await _firestore.collection('products').get();
      return snapshot.docs.map((doc) => _convertDocToProduct(doc)).toList();
    } catch (e) {
      print('Error fetching products: $e');
      return [];
    }
  }

  // Get featured products from Firestore
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('products')
          .where('isFeatured', isEqualTo: true)
          .limit(4)
          .get();
      return snapshot.docs.map((doc) => _convertDocToProduct(doc)).toList();
    } catch (e) {
      print('Error fetching featured products: $e');
      return [];
    }
  }

  // Get products by category
  static Future<List<Product>> getProductsByCategory(
      ProductCategory category) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('products')
          .where('category', isEqualTo: category.toString().split('.').last)
          .get();
      return snapshot.docs.map((doc) => _convertDocToProduct(doc)).toList();
    } catch (e) {
      print('Error fetching products by category: $e');
      return [];
    }
  }

  // Get hublockers from Firestore
  static Future<List<Map<String, dynamic>>> getHublockers() async {
    try {
      final QuerySnapshot snapshot =
          await _firestore.collection('hublockers').get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          'name': data['name'] ?? '',
          'location': data['address'] ?? '', // Use address as location
          'address': data['address'] ?? '',
          'rating': (data['rating'] ?? 0.0).toDouble(),
          'image': data['imageUrl'] ??
              '', // Your Firebase uses 'imageUrl' not 'image'
          'availableProducts': data['availableProducts'] ?? [],
          'city': data['city'] ?? '',
          'district': data['district'] ?? '',
          'capacity': data['capacity'] ?? 0,
          'availableSlots': data['availableSlots'] ?? 0,
          'operatingHours': data['operatingHours'] ?? '',
          'contactPhone': data['contactPhone'] ?? '',
          'isActive': data['isActive'] ?? true,
        };
      }).toList();
    } catch (e) {
      print('Error fetching hublockers: $e');
      return [];
    }
  }

  // Search products
  static Future<List<Product>> searchProducts(String query) async {
    try {
      final QuerySnapshot snapshot =
          await _firestore.collection('products').get();
      final allProducts =
          snapshot.docs.map((doc) => _convertDocToProduct(doc)).toList();

      final lowercaseQuery = query.toLowerCase();
      return allProducts.where((product) {
        return product.name.toLowerCase().contains(lowercaseQuery) ||
            product.description.toLowerCase().contains(lowercaseQuery) ||
            product.tags
                .any((tag) => tag.name.toLowerCase().contains(lowercaseQuery));
      }).toList();
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }

  // Convert Firestore document to Product model
  static Product _convertDocToProduct(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Handle pricing - your Firebase has pricing as an object, not array
    List<ProductPricing> pricingList = [];
    if (data['pricing'] != null) {
      final pricing = data['pricing'] as Map<String, dynamic>;
      if (pricing['daily'] != null) {
        pricingList.add(ProductPricing(
          period: RentalPeriod.day,
          price: (pricing['daily'] ?? 0).toDouble(),
          label: 'Daily',
          displayText: '${pricing['daily']} VND/day',
        ));
      }
      if (pricing['monthly'] != null) {
        pricingList.add(ProductPricing(
          period: RentalPeriod.month,
          price: (pricing['monthly'] ?? 0).toDouble(),
          label: 'Monthly',
          displayText: '${pricing['monthly']} VND/month',
        ));
      }
    }

    // Handle tags - your Firebase has tags as string array, not objects
    List<ProductTag> tagsList = [];
    if (data['tags'] != null) {
      final tags = data['tags'] as List<dynamic>;
      tagsList = tags
          .map((tag) => ProductTag(
                id: tag.toString(),
                name: tag.toString(),
                color: '#708871',
              ))
          .toList();
    }

    return Product(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      images: List<String>.from(data['images'] ?? []),
      category: _stringToCategory(data['category'] ?? 'electronics'),
      additionalCategories: [],
      tags: tagsList,
      pricing: pricingList,
      ownerId: data['ownerId'] ?? '',
      ownerName: data['ownerName'] ?? '',
      ownerAvatar: data['ownerAvatar'] ?? '',
      location: data['location'] ?? '',
      hublockerName: data['hublocker'] ??
          '', // Your Firebase uses 'hublocker' not 'hublockerName'
      hublockerAddress: data['hublockerAddress'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      reviewCount: data['reviewCount'] ?? 0,
      reviews: [],
      isAvailable: data['isAvailable'] ?? true,
      isFeatured: data['isFeatured'] ?? false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      specifications: {
        'condition': data['condition'] ?? '',
        'size': data['size'] ?? '',
        'color': data['color'] ?? '',
        'brand': data['brand'] ?? '',
      },
      availableQuantity: data['availableQuantity'] ?? 1,
    );
  }

  // Helper method to convert string to ProductCategory
  static ProductCategory _stringToCategory(String categoryString) {
    switch (categoryString.toLowerCase()) {
      case 'electronics':
        return ProductCategory.electronics;
      case 'furniture':
        return ProductCategory.furniture;
      case 'clothing':
        return ProductCategory.clothing;
      case 'books':
        return ProductCategory.books;
      case 'sports':
        return ProductCategory.sports;
      case 'household':
        return ProductCategory.household;
      case 'appliances':
        return ProductCategory.appliances;
      case 'tools':
        return ProductCategory.tools;
      case 'automotive':
        return ProductCategory.automotive;
      case 'gaming':
        return ProductCategory.gaming;
      default:
        return ProductCategory.electronics;
    }
  }

  // Helper method to convert string to RentalPeriod
  static RentalPeriod _stringToRentalPeriod(String periodString) {
    switch (periodString.toLowerCase()) {
      case 'day':
        return RentalPeriod.day;
      case 'week':
        return RentalPeriod.week;
      case 'month':
        return RentalPeriod.month;
      case 'quarter':
        return RentalPeriod.quarter;
      case 'year':
        return RentalPeriod.year;
      default:
        return RentalPeriod.day;
    }
  }
}
