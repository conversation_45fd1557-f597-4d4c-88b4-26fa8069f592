import 'package:flutter/material.dart';
import 'package:lockerhub/models/product.dart';
import 'package:lockerhub/data/sample_products.dart';
import 'package:lockerhub/services/firestore_service.dart';
import 'package:lockerhub/screens/enhanced_product_detail_screen.dart';

class AllProductsScreen extends StatefulWidget {
  final bool isDarkMode;

  const AllProductsScreen({
    super.key,
    this.isDarkMode = false,
  });

  @override
  State<AllProductsScreen> createState() => _AllProductsScreenState();
}

class _AllProductsScreenState extends State<AllProductsScreen> {
  String searchQuery = '';
  List<Product> allProducts = [];
  List<Product> filteredProducts = [];
  ProductCategory? selectedCategory;
  String sortBy = 'name'; // name, price_low, price_high, rating

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  void _loadProducts() async {
    try {
      // Load real data from Firebase
      final firestoreProducts = await FirestoreService.getAllProducts();
      setState(() {
        allProducts = firestoreProducts.isNotEmpty
            ? firestoreProducts
            : SampleProducts.getAllProducts();
        filteredProducts = List.from(allProducts);
        _sortProducts();
      });
    } catch (e) {
      print('Error loading products: $e');
      // Fallback to sample data if Firestore fails
      setState(() {
        allProducts = SampleProducts.getAllProducts();
        filteredProducts = List.from(allProducts);
        _sortProducts();
      });
    }
  }

  void _filterProducts(String query) {
    setState(() {
      searchQuery = query;
      _applyFilters();
    });
  }

  void _filterByCategory(ProductCategory? category) {
    setState(() {
      selectedCategory = category;
      _applyFilters();
    });
  }

  void _applyFilters() {
    filteredProducts = allProducts.where((product) {
      // Search filter
      bool matchesSearch = searchQuery.isEmpty ||
          product.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          product.description
              .toLowerCase()
              .contains(searchQuery.toLowerCase()) ||
          product.tags.any((tag) =>
              tag.name.toLowerCase().contains(searchQuery.toLowerCase())) ||
          product.categoryDisplayName
              .toLowerCase()
              .contains(searchQuery.toLowerCase());

      // Category filter
      bool matchesCategory = selectedCategory == null ||
          product.belongsToCategory(selectedCategory!);

      return matchesSearch && matchesCategory;
    }).toList();

    _sortProducts();
  }

  void _sortProducts() {
    switch (sortBy) {
      case 'name':
        filteredProducts.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'price_low':
        filteredProducts.sort((a, b) =>
            a.cheapestPricing.price.compareTo(b.cheapestPricing.price));
        break;
      case 'price_high':
        filteredProducts.sort((a, b) =>
            b.cheapestPricing.price.compareTo(a.cheapestPricing.price));
        break;
      case 'rating':
        filteredProducts.sort((a, b) => b.rating.compareTo(a.rating));
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor:
            widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'All Products',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.sort,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
            onPressed: _showSortOptions,
          ),
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
            onPressed: _showFilterOptions,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: widget.isDarkMode
                      ? Colors.white.withOpacity(0.1)
                      : Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              onChanged: _filterProducts,
              style: TextStyle(
                color: widget.isDarkMode ? Colors.white : Colors.black,
              ),
              decoration: InputDecoration(
                hintText: 'Search products...',
                hintStyle: TextStyle(
                  color: widget.isDarkMode
                      ? Colors.grey.shade400
                      : Colors.grey.shade600,
                ),
                border: InputBorder.none,
                icon: Icon(
                  Icons.search,
                  color: widget.isDarkMode
                      ? Colors.grey.shade400
                      : const Color(0xFF708871),
                ),
              ),
            ),
          ),

          // Active Filters
          if (selectedCategory != null)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Text(
                    'Filters: ',
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.isDarkMode
                          ? Colors.grey.shade300
                          : Colors.grey.shade600,
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF708871).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: const Color(0xFF708871)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          SampleProducts.getCategoryDisplayName(
                              selectedCategory!),
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF708871),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        GestureDetector(
                          onTap: () => _filterByCategory(null),
                          child: const Icon(
                            Icons.close,
                            size: 14,
                            color: Color(0xFF708871),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

          // Results Count
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Text(
                  '${filteredProducts.length} product${filteredProducts.length != 1 ? 's' : ''} found',
                  style: TextStyle(
                    fontSize: 14,
                    color: widget.isDarkMode
                        ? Colors.grey.shade300
                        : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Products List
          Expanded(
            child: filteredProducts.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: filteredProducts.length,
                    itemBuilder: (context, index) {
                      return _buildProductCard(filteredProducts[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color:
                widget.isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No products found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode
                  ? Colors.grey.shade400
                  : Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search terms or filters',
            style: TextStyle(
              fontSize: 14,
              color: widget.isDarkMode
                  ? Colors.grey.shade500
                  : Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(Product product) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EnhancedProductDetailScreen(
              product: product,
              isDarkMode: widget.isDarkMode,
            ),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: widget.isDarkMode
                  ? Colors.white.withOpacity(0.1)
                  : Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Product Image
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(0xFF708871).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: product.images.isNotEmpty
                    ? Image.asset(
                        product.images.first,
                        fit: BoxFit.cover,
                        width: 80,
                        height: 80,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            _getCategoryIcon(product.category),
                            color: const Color(0xFF708871),
                            size: 40,
                          );
                        },
                      )
                    : Icon(
                        _getCategoryIcon(product.category),
                        color: const Color(0xFF708871),
                        size: 40,
                      ),
              ),
            ),

            const SizedBox(width: 16),

            // Product Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: widget.isDarkMode
                          ? Colors.white
                          : const Color(0xFF708871),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'by ${product.ownerName}',
                    style: TextStyle(
                      fontSize: 12,
                      color: widget.isDarkMode
                          ? Colors.grey.shade300
                          : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.star, color: Colors.orange, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        product.rating.toString(),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color:
                              widget.isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '(${product.reviewCount})',
                        style: TextStyle(
                          fontSize: 12,
                          color: widget.isDarkMode
                              ? Colors.grey.shade300
                              : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    product.priceRange,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: widget.isDarkMode
                          ? Colors.white
                          : const Color(0xFF708871),
                    ),
                  ),
                ],
              ),
            ),

            // Favorite and Arrow
            Column(
              children: [
                IconButton(
                  icon: const Icon(
                    Icons.favorite_border,
                    color: Colors.grey,
                    size: 20,
                  ),
                  onPressed: () {
                    // TODO: Add to favorites
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Added to favorites'),
                        backgroundColor: Color(0xFF708871),
                        duration: Duration(seconds: 1),
                      ),
                    );
                  },
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: widget.isDarkMode
                      ? Colors.grey.shade400
                      : Colors.grey.shade600,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(ProductCategory category) {
    switch (category) {
      case ProductCategory.electronics:
        return Icons.electrical_services;
      case ProductCategory.furniture:
        return Icons.chair;
      case ProductCategory.clothing:
        return Icons.checkroom;
      case ProductCategory.books:
        return Icons.book;
      case ProductCategory.sports:
        return Icons.sports_tennis;
      case ProductCategory.household:
        return Icons.home;
      case ProductCategory.appliances:
        return Icons.kitchen;
      case ProductCategory.tools:
        return Icons.build;
      case ProductCategory.automotive:
        return Icons.directions_car;
      case ProductCategory.gaming:
        return Icons.sports_esports;
    }
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Sort by',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: widget.isDarkMode
                      ? Colors.white
                      : const Color(0xFF708871),
                ),
              ),
              const SizedBox(height: 16),
              _buildSortOption('Name (A-Z)', 'name'),
              _buildSortOption('Price (Low to High)', 'price_low'),
              _buildSortOption('Price (High to Low)', 'price_high'),
              _buildSortOption('Rating (High to Low)', 'rating'),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSortOption(String title, String value) {
    final isSelected = sortBy == value;

    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          color: isSelected
              ? const Color(0xFF708871)
              : (widget.isDarkMode ? Colors.white : Colors.black),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      trailing:
          isSelected ? const Icon(Icons.check, color: Color(0xFF708871)) : null,
      onTap: () {
        setState(() {
          sortBy = value;
          _sortProducts();
        });
        Navigator.pop(context);
      },
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Filter by Category',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: widget.isDarkMode
                      ? Colors.white
                      : const Color(0xFF708871),
                ),
              ),
              const SizedBox(height: 16),
              _buildFilterOption('All Categories', null),
              ...ProductCategory.values
                  .map((category) => _buildFilterOption(
                      SampleProducts.getCategoryDisplayName(category),
                      category))
                  .toList(),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFilterOption(String title, ProductCategory? category) {
    final isSelected = selectedCategory == category;

    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          color: isSelected
              ? const Color(0xFF708871)
              : (widget.isDarkMode ? Colors.white : Colors.black),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      trailing:
          isSelected ? const Icon(Icons.check, color: Color(0xFF708871)) : null,
      onTap: () {
        _filterByCategory(category);
        Navigator.pop(context);
      },
    );
  }
}
