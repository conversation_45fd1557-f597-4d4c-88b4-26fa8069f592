// Script to add products to your Firebase database
const admin = require('firebase-admin');

async function addProductsToFirebase() {
  try {
    console.log('🚀 Adding products to Firebase...\n');

    // Initialize Firebase Admin
    const app = admin.initializeApp({
      projectId: 'pinggo-351c6'
    });
    
    const db = admin.firestore(app);

    // Products to add
    const products = [
      {
        id: 'product_gaming_laptop',
        name: 'Gaming Laptop',
        description: 'High-performance gaming laptop with RTX graphics card. Perfect for gaming, video editing, and intensive tasks.',
        category: 'Electronics',
        subcategory: 'Computers',
        images: ['lib/images/product/gaming_laptop.jpg'],
        pricing: {
          daily: 150000,    // 150,000 VND per day
          monthly: 3000000, // 3,000,000 VND per month
          quarterly: 8000000, // 8,000,000 VND per quarter
          yearly: 25000000  // 25,000,000 VND per year
        },
        ownerId: 'user_demo_owner1',
        ownerName: '<PERSON><PERSON><PERSON>',
        ownerPhone: '+84 987 654 321',
        hublocker: 'hublocker_bachkhoa',
        hublockerId: 'hublocker_bachkhoa',
        tags: ['gaming', 'laptop', 'electronics', 'high-performance', 'rtx'],
        isAvailable: true,
        isFeatured: true,
        condition: 'excellent',
        brand: 'ASUS ROG',
        rating: 4.8,
        reviewCount: 15,
        rentCount: 32
      },
      {
        id: 'product_professional_camera',
        name: 'Professional Camera',
        description: 'DSLR camera with multiple lenses. Perfect for photography projects, events, and professional shoots.',
        category: 'Electronics',
        subcategory: 'Photography',
        images: ['lib/images/product/camera.jpg'],
        pricing: {
          daily: 200000,    // 200,000 VND per day
          monthly: 4000000, // 4,000,000 VND per month
        },
        ownerId: 'user_demo_owner2',
        ownerName: 'Tran Thi B',
        ownerPhone: '+84 123 456 789',
        hublocker: 'hublocker_ilogic',
        hublockerId: 'hublocker_ilogic',
        tags: ['camera', 'photography', 'professional', 'dslr', 'lenses'],
        isAvailable: true,
        isFeatured: true,
        condition: 'excellent',
        brand: 'Canon EOS',
        rating: 4.9,
        reviewCount: 8,
        rentCount: 18
      },
      {
        id: 'product_projector',
        name: 'HD Projector',
        description: 'High-definition projector for presentations, movies, and events. Includes screen and cables.',
        category: 'Electronics',
        subcategory: 'Audio/Video',
        images: ['lib/images/product/projector.jpg'],
        pricing: {
          daily: 100000,    // 100,000 VND per day
          monthly: 2000000, // 2,000,000 VND per month
        },
        ownerId: 'user_demo_owner3',
        ownerName: 'Le Van C',
        ownerPhone: '+84 369 852 147',
        hublocker: 'hublocker_bachkhoa',
        hublockerId: 'hublocker_bachkhoa',
        tags: ['projector', 'presentation', 'hd', 'movies', 'events'],
        isAvailable: true,
        isFeatured: false,
        condition: 'good',
        brand: 'Epson',
        rating: 4.5,
        reviewCount: 12,
        rentCount: 28
      },
      {
        id: 'product_bluetooth_speaker',
        name: 'Bluetooth Speaker',
        description: 'Portable high-quality Bluetooth speaker with excellent sound quality. Perfect for parties and outdoor events.',
        category: 'Electronics',
        subcategory: 'Audio',
        images: ['lib/images/product/speaker.jpg'],
        pricing: {
          daily: 50000,     // 50,000 VND per day
          monthly: 1000000, // 1,000,000 VND per month
        },
        ownerId: 'user_demo_owner4',
        ownerName: 'Pham Thi D',
        ownerPhone: '+84 147 258 369',
        hublocker: 'hublocker_ilogic_goldenview',
        hublockerId: 'hublocker_ilogic_goldenview',
        tags: ['speaker', 'bluetooth', 'portable', 'music', 'party'],
        isAvailable: true,
        isFeatured: true,
        condition: 'excellent',
        brand: 'JBL',
        rating: 4.7,
        reviewCount: 20,
        rentCount: 45
      },
      {
        id: 'product_office_chair',
        name: 'Ergonomic Office Chair',
        description: 'Comfortable ergonomic office chair with lumbar support. Perfect for long working sessions.',
        category: 'Furniture',
        subcategory: 'Office',
        images: ['lib/images/product/office_chair.jpg'],
        pricing: {
          daily: 75000,     // 75,000 VND per day
          monthly: 1500000, // 1,500,000 VND per month
        },
        ownerId: 'user_demo_owner5',
        ownerName: 'Hoang Van E',
        ownerPhone: '+84 258 147 369',
        hublocker: 'hublocker_vanhoa',
        hublockerId: 'hublocker_vanhoa',
        tags: ['chair', 'office', 'ergonomic', 'furniture', 'comfort'],
        isAvailable: true,
        isFeatured: false,
        condition: 'good',
        brand: 'Herman Miller',
        rating: 4.6,
        reviewCount: 10,
        rentCount: 22
      }
    ];

    console.log('📦 Adding products to Firestore...');
    for (const product of products) {
      const { id, ...data } = product;
      await db.collection('products').doc(id).set(data);
      console.log(`✅ Added product: ${product.name}`);
    }

    console.log('\n🎉 All products added successfully!');
    console.log('\n📊 Products Summary:');
    console.log(`   • ${products.length} products created`);
    console.log(`   • ${products.filter(p => p.isFeatured).length} featured products`);
    console.log('\n🔗 Access your data:');
    console.log('   • Firebase Console: https://console.firebase.google.com/project/pinggo-351c6/firestore');
    console.log('\n🚀 Your products are now available in the app!');
    
    await app.delete();
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n💡 Make sure you have Firebase CLI installed and are authenticated:');
    console.log('   npm install -g firebase-tools');
    console.log('   firebase login');
  }
}

addProductsToFirebase();
