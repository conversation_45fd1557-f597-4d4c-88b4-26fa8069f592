import 'package:flutter/material.dart';
import 'locker_detail_screen.dart';
import 'package:lockerhub/screens/search_screen.dart';
import 'package:lockerhub/screens/notifications_screen.dart';
import 'package:lockerhub/models/product.dart';
import 'package:lockerhub/data/sample_products.dart';
import 'package:lockerhub/services/firestore_service.dart';
import 'package:lockerhub/screens/category_products_screen.dart';
import 'package:lockerhub/screens/enhanced_product_detail_screen.dart';
import 'package:lockerhub/screens/all_hublockers_screen.dart';
import 'package:lockerhub/screens/all_products_screen.dart';

class HomeScreen extends StatefulWidget {
  final bool isDarkMode;

  const HomeScreen({super.key, this.isDarkMode = false});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<Product> _featuredProducts = [];
  List<Map<String, dynamic>> _hublockers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // TEMPORARY: Use sample data while Firebase is being configured
      // This ensures the app shows content immediately
      setState(() {
        _featuredProducts = SampleProducts.getFeaturedProducts();
        _hublockers = _getSampleHublockers();
        _isLoading = false;
      });

      // Original Firestore code (will be restored when Firebase is configured)
      /*
      final featuredProducts = await FirestoreService.getFeaturedProducts();
      final hublockers = await FirestoreService.getHublockers();

      setState(() {
        _featuredProducts = featuredProducts;
        _hublockers = hublockers;
        _isLoading = false;
      });
      */
    } catch (e) {
      print('Error loading data: $e');
      // Fallback to sample data if Firestore fails
      setState(() {
        _featuredProducts = SampleProducts.getFeaturedProducts();
        _hublockers = _getSampleHublockers();
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> _getSampleHublockers() {
    return [
      {
        'name': 'HUST Hublocker',
        'location': 'Hanoi University of Science and Technology',
        'address': 'No 1b, Ta Quang Buu Street',
        'rating': 4.5,
        'image': 'lib/images/locker.png',
        'availableProducts': [],
      },
      {
        'name': 'Logic Box Golden View',
        'location': 'Golden View Building, District 4',
        'address': 'Golden View Building, District 4',
        'rating': 4.5,
        'image': 'lib/images/locker.png',
        'availableProducts': [],
      },
      {
        'name': 'FPT University Hub',
        'location': 'FPT University Campus',
        'address': 'FPT University Campus',
        'rating': 4.3,
        'image': 'lib/images/locker.png',
        'availableProducts': [],
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor:
            widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.menu,
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
          ),
          onPressed: () {
            Scaffold.of(context).openDrawer();
          },
        ),
        title: Text(
          'PingGo',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search Bar
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const SearchScreen()),
                );
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color:
                      widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: widget.isDarkMode
                        ? Colors.white30
                        : Colors.grey.shade300,
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      'Search',
                      style: TextStyle(
                        color: widget.isDarkMode ? Colors.white70 : Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      Icons.search,
                      color: widget.isDarkMode
                          ? Colors.white70
                          : Colors.grey.shade600,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Collection Banner
            Container(
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF708871),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.black.withOpacity(0.3),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    const Positioned(
                      top: 16,
                      left: 16,
                      child: Text(
                        'Collection',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 16,
                      left: 16,
                      child: ElevatedButton(
                        onPressed: () {
                          // TODO: Navigate to collection
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF708871),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        child: const Text('Rent Now'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Category Icons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildCategoryIcon(Icons.electrical_services, 'Electronics',
                    Colors.blue, ProductCategory.electronics),
                _buildCategoryIcon(Icons.chair, 'Furniture', Colors.brown,
                    ProductCategory.furniture),
                _buildCategoryIcon(Icons.home_outlined, 'Household',
                    const Color(0xFF708871), ProductCategory.household),
                _buildCategoryIcon(Icons.sports_tennis, 'Sports', Colors.orange,
                    ProductCategory.sports),
                _buildCategoryIcon(Icons.build, 'Tools', Colors.grey.shade700,
                    ProductCategory.tools),
              ],
            ),

            const SizedBox(height: 24),

            // Our Hublocker Section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Our Hublocker',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: widget.isDarkMode
                        ? Colors.white
                        : const Color(0xFF708871),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => AllHublockersScreen(
                          isDarkMode: widget.isDarkMode,
                        ),
                      ),
                    );
                  },
                  child: Text(
                    'View All',
                    style: TextStyle(
                      color: widget.isDarkMode
                          ? Colors.white
                          : const Color(0xFF708871),
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Hublocker Cards
            SizedBox(
              height: 200,
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _hublockers.length,
                      itemBuilder: (context, index) {
                        final hublocker = _hublockers[index];
                        final colors = [Colors.red, Colors.blue, Colors.orange];
                        return Padding(
                          padding: EdgeInsets.only(
                              right: index < _hublockers.length - 1 ? 16 : 0),
                          child: _buildHublockerCard(
                            hublocker['name'] ?? '',
                            hublocker['image'] ?? 'lib/images/locker.png',
                            hublocker['rating']?.toDouble() ?? 0.0,
                            colors[index % colors.length],
                            hublocker['location'] ?? '',
                          ),
                        );
                      },
                    ),
            ),

            const SizedBox(height: 32),

            // Available Products Section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Available Products',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: widget.isDarkMode
                        ? Colors.white
                        : const Color(0xFF708871),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => AllProductsScreen(
                          isDarkMode: widget.isDarkMode,
                        ),
                      ),
                    );
                  },
                  child: Text(
                    'View All',
                    style: TextStyle(
                      color: widget.isDarkMode
                          ? Colors.white
                          : const Color(0xFF708871),
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Products Grid
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.8,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                    ),
                    itemCount: _featuredProducts.length.clamp(0, 4),
                    itemBuilder: (context, index) {
                      return _buildProductCard(_featuredProducts[index]);
                    },
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryIcon(
      IconData icon, String label, Color color, ProductCategory category) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CategoryProductsScreen(
              category: category,
              isDarkMode: widget.isDarkMode,
            ),
          ),
        );
      },
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHublockerCard(String name, String imagePath, double rating,
      Color accentColor, String address) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => LockerDetailScreen(
              hublocker: {
                'name': name,
                'location': address,
                'rating': rating,
                'image': imagePath,
                'availableProducts': _getAvailableProducts(name),
              },
            ),
          ),
        );
      },
      child: Container(
        width: 160,
        decoration: BoxDecoration(
          color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: widget.isDarkMode
                  ? Colors.white.withOpacity(0.1)
                  : Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 120,
              decoration: BoxDecoration(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(12)),
                color: accentColor.withOpacity(0.1),
              ),
              child: Center(
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: accentColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.business,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: widget.isDarkMode
                          ? Colors.white
                          : const Color(0xFF708871),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        rating.toString(),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color:
                              widget.isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      const SizedBox(width: 4),
                      const Icon(
                        Icons.star,
                        color: Colors.orange,
                        size: 16,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getAvailableProducts(String hublockerName) {
    // Sample products for each hublocker
    switch (hublockerName) {
      case 'HUST Hublocker':
        return [
          {
            'name': 'Gaming Chair Pro',
            'owner': 'Nguyen Van A',
            'phone': '0123456789',
            'fee': '50,000 VND/day',
          },
          {
            'name': 'Laptop Stand',
            'owner': 'Tran Thi B',
            'phone': '0987654321',
            'fee': '20,000 VND/day',
          },
          {
            'name': 'Desk Lamp LED',
            'owner': 'Le Van C',
            'phone': '0369852147',
            'fee': '15,000 VND/day',
          },
        ];
      case 'Logic Box Golden View':
        return [
          {
            'name': 'Coffee Maker',
            'owner': 'Pham Thi D',
            'phone': '0147258369',
            'fee': '30,000 VND/day',
          },
          {
            'name': 'Bluetooth Speaker',
            'owner': 'Hoang Van E',
            'phone': '0258147369',
            'fee': '25,000 VND/day',
          },
        ];
      case 'FPT University Hub':
        return [
          {
            'name': 'Projector',
            'owner': 'Vu Thi F',
            'phone': '0741852963',
            'fee': '100,000 VND/day',
          },
          {
            'name': 'Whiteboard',
            'owner': 'Do Van G',
            'phone': '0963741852',
            'fee': '40,000 VND/day',
          },
        ];
      default:
        return [];
    }
  }

  Widget _buildProductCard(Product product) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EnhancedProductDetailScreen(
              product: product,
              isDarkMode: widget.isDarkMode,
            ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: widget.isDarkMode
                  ? Colors.white.withOpacity(0.1)
                  : Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(12)),
                      color: const Color(0xFF708871).withOpacity(0.1),
                    ),
                    child: ClipRRect(
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(12)),
                      child: product.images.isNotEmpty
                          ? Image.asset(
                              product.images.first,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                              errorBuilder: (context, error, stackTrace) {
                                return Center(
                                  child: Icon(
                                    _getProductIcon(product.category),
                                    size: 40,
                                    color: const Color(0xFF708871),
                                  ),
                                );
                              },
                            )
                          : Center(
                              child: Icon(
                                _getProductIcon(product.category),
                                size: 40,
                                color: const Color(0xFF708871),
                              ),
                            ),
                    ),
                  ),
                  // Favorite Heart Button
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          // Toggle favorite status (you can implement proper state management)
                        });
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Added to favorites'),
                            backgroundColor: Color(0xFF708871),
                            duration: Duration(seconds: 1),
                          ),
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.favorite_border,
                          size: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      product.name,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: widget.isDarkMode ? Colors.white : Colors.black,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.priceRange,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: widget.isDarkMode
                                ? Colors.white
                                : const Color(0xFF708871),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.orange,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              product.rating.toString(),
                              style: TextStyle(
                                fontSize: 12,
                                color: widget.isDarkMode
                                    ? Colors.grey[300]
                                    : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getProductIcon(ProductCategory category) {
    switch (category) {
      case ProductCategory.electronics:
        return Icons.electrical_services;
      case ProductCategory.furniture:
        return Icons.chair;
      case ProductCategory.clothing:
        return Icons.checkroom;
      case ProductCategory.books:
        return Icons.book;
      case ProductCategory.sports:
        return Icons.sports_tennis;
      case ProductCategory.household:
        return Icons.home;
      case ProductCategory.appliances:
        return Icons.kitchen;
      case ProductCategory.tools:
        return Icons.build;
      case ProductCategory.automotive:
        return Icons.directions_car;
      case ProductCategory.gaming:
        return Icons.sports_esports;
    }
  }
}
