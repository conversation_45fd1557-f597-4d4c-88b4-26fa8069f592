// Simple approach - let's just create the data structure for manual entry
console.log('📋 Creating hublocker data for manual Firebase entry...');

const hublockers = [
  {
    id: 'hublocker_bachkhoa',
    name: '<PERSON>',
    location: 'Hanoi University of Science and Technology',
    address: 'No 1b, Ta Quang Buu Street, Hanoi',
    rating: 4.8,
    image: 'lib/images/locker.png',
    availableProducts: [],
    isActive: true,
  },
  {
    id: 'hublocker_ilogic',
    name: 'iLogic Hublocker',
    location: 'iLogic Building, District 1',
    address: 'iLogic Building, District 1, Ho Chi Minh City',
    rating: 4.6,
    image: 'lib/images/locker.png',
    availableProducts: [],
    isActive: true,
  },
  {
    id: 'hublocker_fpt',
    name: 'FPT University Hublocker',
    location: 'FPT University Campus',
    address: 'FPT University Campus, Ho Chi Minh City',
    rating: 4.5,
    image: 'lib/images/locker.png',
    availableProducts: [],
    isActive: true,
  }
];

console.log('\n🔥 HUBLOCKERS DATA FOR FIREBASE:');
console.log('Copy this JSON and add it to Firebase Console manually:\n');

const firestoreData = {};
hublockers.forEach(hublocker => {
  firestoreData[hublocker.id] = hublocker;
});

console.log(JSON.stringify(firestoreData, null, 2));

console.log('\n📋 INSTRUCTIONS:');
console.log('1. Go to Firebase Console: https://console.firebase.google.com/project/pinggo-351c6/firestore');
console.log('2. Create a collection called "hublockers"');
console.log('3. Add each document with the ID and data shown above');
console.log('4. Restart your Flutter app');

console.log('\n✨ Done! Use this data to manually add to Firebase.');
