import '../models/product.dart';

class SampleProducts {
  static List<Product> getAllProducts() {
    return [
      // Electronics
      Product(
        id: '1',
        name: 'Gaming Laptop',
        description:
            'High-performance gaming laptop with RTX 4060, perfect for gaming and professional work. Features RGB keyboard, 144Hz display, and excellent cooling system.',
        images: ['images/product/ElectricPlug.jpg'],
        category: ProductCategory.electronics,
        additionalCategories: [ProductCategory.gaming],
        tags: [
          ProductTag(id: '1', name: 'Gaming', color: '#FF6B6B'),
          ProductTag(id: '2', name: 'High Performance', color: '#4ECDC4'),
          ProductTag(id: '3', name: 'RGB', color: '#45B7D1'),
        ],
        pricing: [
          ProductPricing(
              period: RentalPeriod.day,
              price: 150000,
              label: 'Day',
              displayText: '150,000 VND/day'),
          ProductPricing(
              period: RentalPeriod.week,
              price: 900000,
              label: 'Week',
              displayText: '900,000 VND/week'),
          ProductPricing(
              period: RentalPeriod.month,
              price: 3000000,
              label: 'Month',
              displayText: '3,000,000 VND/month'),
        ],
        ownerId: 'owner1',
        ownerName: '<PERSON>',
        ownerAvatar: 'AC',
        location: 'Hanoi',
        hublockerName: 'HUST Hublocker',
        hublockerAddress: 'No 1b, Ta Quang Buu Street',
        rating: 4.8,
        reviewCount: 24,
        reviews: [
          ProductReview(
            id: '1',
            userName: 'Gaming Pro',
            userAvatar: 'GP',
            rating: 5.0,
            comment: 'Amazing laptop! Perfect for gaming and streaming.',
            date: DateTime.now().subtract(const Duration(days: 2)),
          ),
        ],
        isAvailable: true,
        isFeatured: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        specifications: {
          'CPU': 'Intel i7-12700H',
          'GPU': 'RTX 4060 8GB',
          'RAM': '16GB DDR5',
          'Storage': '1TB NVMe SSD',
          'Display': '15.6" 144Hz',
        },
        availableQuantity: 2,
      ),

      // Furniture
      Product(
        id: '2',
        name: 'Modern Lemon Chair',
        description:
            'Relax in ultimate comfort with this plush chair, designed to cradle your body in serenity. Soft cushions, ergonomic support, and a sturdy base ensure a cozy retreat for relaxing, working, or unwinding.',
        images: ['images/product/HUSTuniform.jpg'],
        category: ProductCategory.furniture,
        tags: [
          ProductTag(id: '4', name: 'Comfortable', color: '#96CEB4'),
          ProductTag(id: '5', name: 'Modern', color: '#FFEAA7'),
          ProductTag(id: '6', name: 'Ergonomic', color: '#DDA0DD'),
        ],
        pricing: [
          ProductPricing(
              period: RentalPeriod.day,
              price: 50000,
              label: 'Day',
              displayText: '50,000 VND/day'),
          ProductPricing(
              period: RentalPeriod.week,
              price: 300000,
              label: 'Week',
              displayText: '300,000 VND/week'),
          ProductPricing(
              period: RentalPeriod.month,
              price: 1000000,
              label: 'Month',
              displayText: '1,000,000 VND/month'),
          ProductPricing(
              period: RentalPeriod.quarter,
              price: 2700000,
              label: 'Quarter',
              displayText: '2,700,000 VND/quarter'),
        ],
        ownerId: 'owner2',
        ownerName: 'Joe Trump',
        ownerAvatar: 'JT',
        location: 'Hanoi',
        hublockerName: 'HUST Hublocker',
        hublockerAddress: 'No 1b, Ta Quang Buu Street',
        rating: 4.5,
        reviewCount: 18,
        isAvailable: true,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        specifications: {
          'Material': 'Premium Fabric',
          'Dimensions': '80cm x 85cm x 95cm',
          'Weight': '25kg',
          'Color': 'Lemon Yellow',
        },
        availableQuantity: 1,
      ),

      // Electronics - Appliances
      Product(
        id: '3',
        name: 'Smart Coffee Maker',
        description:
            'WiFi-enabled coffee maker with app control, programmable brewing, and premium quality construction. Perfect for coffee enthusiasts who want convenience and quality.',
        images: ['images/product/Umbrella.jpg'],
        category: ProductCategory.appliances,
        additionalCategories: [ProductCategory.electronics],
        tags: [
          ProductTag(id: '7', name: 'Smart', color: '#74B9FF'),
          ProductTag(id: '8', name: 'WiFi', color: '#00B894'),
          ProductTag(id: '9', name: 'Premium', color: '#FDCB6E'),
        ],
        pricing: [
          ProductPricing(
              period: RentalPeriod.day,
              price: 30000,
              label: 'Day',
              displayText: '30,000 VND/day'),
          ProductPricing(
              period: RentalPeriod.week,
              price: 180000,
              label: 'Week',
              displayText: '180,000 VND/week'),
          ProductPricing(
              period: RentalPeriod.month,
              price: 600000,
              label: 'Month',
              displayText: '600,000 VND/month'),
        ],
        ownerId: 'owner3',
        ownerName: 'Sarah Wilson',
        ownerAvatar: 'SW',
        location: 'Ho Chi Minh',
        hublockerName: 'FPT Hublocker',
        hublockerAddress: 'FPT University Campus',
        rating: 4.6,
        reviewCount: 12,
        isAvailable: true,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
        specifications: {
          'Capacity': '12 cups',
          'Features': 'WiFi, App Control, Timer',
          'Material': 'Stainless Steel',
          'Power': '1200W',
        },
        availableQuantity: 3,
      ),

      // Sports Equipment
      Product(
        id: '4',
        name: 'Professional Tennis Racket',
        description:
            'High-quality tennis racket used by professionals. Perfect weight balance, excellent string tension, and comfortable grip for optimal performance.',
        images: ['images/product/AoMuaAgribank.jpg'],
        category: ProductCategory.sports,
        tags: [
          ProductTag(id: '10', name: 'Professional', color: '#E17055'),
          ProductTag(id: '11', name: 'Lightweight', color: '#81ECEC'),
        ],
        pricing: [
          ProductPricing(
              period: RentalPeriod.day,
              price: 25000,
              label: 'Day',
              displayText: '25,000 VND/day'),
          ProductPricing(
              period: RentalPeriod.week,
              price: 150000,
              label: 'Week',
              displayText: '150,000 VND/week'),
          ProductPricing(
              period: RentalPeriod.month,
              price: 500000,
              label: 'Month',
              displayText: '500,000 VND/month'),
        ],
        ownerId: 'owner4',
        ownerName: 'Mike Johnson',
        ownerAvatar: 'MJ',
        location: 'Da Nang',
        hublockerName: 'UET Hublocker',
        hublockerAddress: 'University of Engineering and Technology',
        rating: 4.7,
        reviewCount: 8,
        isAvailable: true,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        specifications: {
          'Weight': '300g',
          'Head Size': '100 sq in',
          'String Pattern': '16x19',
          'Grip Size': '4 1/4',
        },
        availableQuantity: 2,
      ),

      // Books
      Product(
        id: '5',
        name: 'Advanced Calculus Textbook',
        description:
            'Comprehensive calculus textbook covering advanced topics. Perfect for university students and professionals looking to enhance their mathematical knowledge.',
        images: ['images/product/Calculus1.jpg'],
        category: ProductCategory.books,
        tags: [
          ProductTag(id: '12', name: 'Educational', color: '#A29BFE'),
          ProductTag(id: '13', name: 'University', color: '#6C5CE7'),
        ],
        pricing: [
          ProductPricing(
              period: RentalPeriod.day,
              price: 10000,
              label: 'Day',
              displayText: '10,000 VND/day'),
          ProductPricing(
              period: RentalPeriod.week,
              price: 60000,
              label: 'Week',
              displayText: '60,000 VND/week'),
          ProductPricing(
              period: RentalPeriod.month,
              price: 200000,
              label: 'Month',
              displayText: '200,000 VND/month'),
          ProductPricing(
              period: RentalPeriod.quarter,
              price: 500000,
              label: 'Quarter',
              displayText: '500,000 VND/quarter'),
        ],
        ownerId: 'owner5',
        ownerName: 'Dr. Emily Chen',
        ownerAvatar: 'EC',
        location: 'Hanoi',
        hublockerName: 'HUST Hublocker',
        hublockerAddress: 'No 1b, Ta Quang Buu Street',
        rating: 4.3,
        reviewCount: 15,
        isAvailable: true,
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
        specifications: {
          'Author': 'Stewart, Clegg & Watson',
          'Edition': '9th Edition',
          'Pages': '1368',
          'Publisher': 'Cengage Learning',
        },
        availableQuantity: 4,
      ),

      // Tools
      Product(
        id: '6',
        name: 'Professional Drill Set',
        description:
            'Complete drill set with multiple bits and accessories. Perfect for home improvement projects, furniture assembly, and professional construction work.',
        images: ['images/product/Conan.jpg'],
        category: ProductCategory.tools,
        tags: [
          ProductTag(id: '14', name: 'Professional', color: '#E17055'),
          ProductTag(id: '15', name: 'Complete Set', color: '#00B894'),
          ProductTag(id: '16', name: 'Cordless', color: '#FDCB6E'),
        ],
        pricing: [
          ProductPricing(
              period: RentalPeriod.day,
              price: 80000,
              label: 'Day',
              displayText: '80,000 VND/day'),
          ProductPricing(
              period: RentalPeriod.week,
              price: 450000,
              label: 'Week',
              displayText: '450,000 VND/week'),
          ProductPricing(
              period: RentalPeriod.month,
              price: 1500000,
              label: 'Month',
              displayText: '1,500,000 VND/month'),
        ],
        ownerId: 'owner6',
        ownerName: 'David Brown',
        ownerAvatar: 'DB',
        location: 'Ho Chi Minh',
        hublockerName: 'Logic Box Golden View',
        hublockerAddress: 'Golden View Building, District 4',
        rating: 4.9,
        reviewCount: 22,
        isAvailable: true,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        specifications: {
          'Battery': '18V Li-ion',
          'Chuck Size': '13mm',
          'Torque': '65 Nm',
          'Bits Included': '50+ pieces',
        },
        availableQuantity: 1,
      ),
    ];
  }

  static List<Product> getProductsByCategory(ProductCategory category) {
    return getAllProducts()
        .where((product) => product.belongsToCategory(category))
        .toList();
  }

  static List<Product> getFeaturedProducts() {
    return getAllProducts().where((product) => product.isFeatured).toList();
  }

  static List<Product> searchProducts(String query) {
    final lowercaseQuery = query.toLowerCase();
    return getAllProducts().where((product) {
      return product.name.toLowerCase().contains(lowercaseQuery) ||
          product.description.toLowerCase().contains(lowercaseQuery) ||
          product.tags
              .any((tag) => tag.name.toLowerCase().contains(lowercaseQuery)) ||
          product.categoryDisplayName.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  static Product? getProductById(String id) {
    try {
      return getAllProducts().firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<ProductCategory> getAllCategories() {
    return ProductCategory.values;
  }

  static String getCategoryDisplayName(ProductCategory category) {
    switch (category) {
      case ProductCategory.electronics:
        return 'Electronics';
      case ProductCategory.furniture:
        return 'Furniture';
      case ProductCategory.clothing:
        return 'Clothing';
      case ProductCategory.books:
        return 'Books';
      case ProductCategory.sports:
        return 'Sports';
      case ProductCategory.household:
        return 'Household';
      case ProductCategory.appliances:
        return 'Appliances';
      case ProductCategory.tools:
        return 'Tools';
      case ProductCategory.automotive:
        return 'Automotive';
      case ProductCategory.gaming:
        return 'Gaming';
    }
  }
}
