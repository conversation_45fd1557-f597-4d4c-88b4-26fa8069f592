// Script to check current Firebase data
const admin = require('firebase-admin');

async function checkFirebaseData() {
  try {
    console.log('🔍 Checking Firebase projects for hublocker data...\n');

    // Check Project 1: pinggo-351c6
    console.log('📊 Checking Project: pinggo-351c6');
    console.log('=' .repeat(50));
    
    try {
      const app1 = admin.initializeApp({
        projectId: 'pinggo-351c6'
      }, 'pinggo');
      
      const db1 = admin.firestore(app1);
      const hublockers1 = await db1.collection('hublockers').get();
      
      console.log(`Found ${hublockers1.size} hublockers in pinggo-351c6:`);
      hublockers1.forEach(doc => {
        const data = doc.data();
        console.log(`  • ${data.name || 'Unnamed'} (ID: ${doc.id})`);
        console.log(`    Address: ${data.address || 'No address'}`);
        console.log(`    Active: ${data.isActive ? 'Yes' : 'No'}`);
        console.log(`    Capacity: ${data.capacity || 'Unknown'}`);
        console.log('');
      });
      
      await app1.delete();
    } catch (error) {
      console.log(`❌ Error accessing pinggo-351c6: ${error.message}`);
    }

    console.log('\n' + '=' .repeat(50));
    
    // Check Project 2: lockerhub-5382f
    console.log('📊 Checking Project: lockerhub-5382f');
    console.log('=' .repeat(50));
    
    try {
      const app2 = admin.initializeApp({
        projectId: 'lockerhub-5382f'
      }, 'lockerhub');
      
      const db2 = admin.firestore(app2);
      const hublockers2 = await db2.collection('hublockers').get();
      
      console.log(`Found ${hublockers2.size} hublockers in lockerhub-5382f:`);
      hublockers2.forEach(doc => {
        const data = doc.data();
        console.log(`  • ${data.name || 'Unnamed'} (ID: ${doc.id})`);
        console.log(`    Address: ${data.address || 'No address'}`);
        console.log(`    Active: ${data.isActive ? 'Yes' : 'No'}`);
        console.log(`    Capacity: ${data.capacity || 'Unknown'}`);
        console.log('');
      });
      
      await app2.delete();
    } catch (error) {
      console.log(`❌ Error accessing lockerhub-5382f: ${error.message}`);
    }

    console.log('\n🔗 Firebase Console Links:');
    console.log('  • pinggo-351c6: https://console.firebase.google.com/project/pinggo-351c6/firestore');
    console.log('  • lockerhub-5382f: https://console.firebase.google.com/project/lockerhub-5382f/firestore');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n💡 Make sure you have Firebase CLI installed and are authenticated:');
    console.log('   npm install -g firebase-tools');
    console.log('   firebase login');
  }
}

checkFirebaseData();
