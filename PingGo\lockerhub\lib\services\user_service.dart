import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class UserService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Create user document in Firestore when user signs up
  static Future<void> createUserDocument({
    required String uid,
    required String email,
    String? displayName,
    String? photoURL,
    String? firstName,
    String? lastName,
    String? phoneNumber,
  }) async {
    try {
      final userDoc = _firestore.collection('users').doc(uid);
      
      // Check if user document already exists
      final docSnapshot = await userDoc.get();
      if (docSnapshot.exists) {
        print('User document already exists');
        return;
      }

      // Create new user document
      await userDoc.set({
        'uid': uid,
        'email': email,
        'displayName': displayName ?? '',
        'firstName': firstName ?? '',
        'lastName': lastName ?? '',
        'phoneNumber': phoneNumber ?? '',
        'photoURL': photoURL ?? '',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'isActive': true,
        'role': 'user', // Default role
      });

      print('User document created successfully');
    } catch (e) {
      print('Error creating user document: $e');
      rethrow;
    }
  }

  // Get user data from Firestore
  static Future<Map<String, dynamic>?> getUserData(String uid) async {
    try {
      final userDoc = await _firestore.collection('users').doc(uid).get();
      
      if (userDoc.exists) {
        return userDoc.data();
      } else {
        print('User document does not exist');
        return null;
      }
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }

  // Update user data in Firestore
  static Future<void> updateUserData({
    required String uid,
    String? displayName,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? photoURL,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (displayName != null) updateData['displayName'] = displayName;
      if (firstName != null) updateData['firstName'] = firstName;
      if (lastName != null) updateData['lastName'] = lastName;
      if (phoneNumber != null) updateData['phoneNumber'] = phoneNumber;
      if (photoURL != null) updateData['photoURL'] = photoURL;

      await _firestore.collection('users').doc(uid).update(updateData);
      print('User data updated successfully');
    } catch (e) {
      print('Error updating user data: $e');
      rethrow;
    }
  }

  // Get current user data
  static Future<Map<String, dynamic>?> getCurrentUserData() async {
    final currentUser = _auth.currentUser;
    if (currentUser != null) {
      return await getUserData(currentUser.uid);
    }
    return null;
  }

  // Delete user document (for account deletion)
  static Future<void> deleteUserDocument(String uid) async {
    try {
      await _firestore.collection('users').doc(uid).delete();
      print('User document deleted successfully');
    } catch (e) {
      print('Error deleting user document: $e');
      rethrow;
    }
  }

  // Check if user document exists
  static Future<bool> userDocumentExists(String uid) async {
    try {
      final userDoc = await _firestore.collection('users').doc(uid).get();
      return userDoc.exists;
    } catch (e) {
      print('Error checking user document: $e');
      return false;
    }
  }

  // Get user stream for real-time updates
  static Stream<DocumentSnapshot> getUserStream(String uid) {
    return _firestore.collection('users').doc(uid).snapshots();
  }

  // Search users by email (for admin purposes)
  static Future<List<Map<String, dynamic>>> searchUsersByEmail(String email) async {
    try {
      final querySnapshot = await _firestore
          .collection('users')
          .where('email', isEqualTo: email)
          .get();

      return querySnapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();
    } catch (e) {
      print('Error searching users: $e');
      return [];
    }
  }
}
