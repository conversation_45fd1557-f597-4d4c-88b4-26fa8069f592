<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PingGo Hublocker Data Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .project-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .project-title {
            color: #1976d2;
            font-size: 1.5em;
            margin-bottom: 10px;
        }
        .hublocker-card {
            background: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .hublocker-name {
            font-weight: bold;
            color: #333;
            font-size: 1.2em;
        }
        .hublocker-details {
            margin-top: 8px;
            color: #666;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .active { background: #e8f5e8; color: #2e7d32; }
        .inactive { background: #ffebee; color: #c62828; }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 PingGo Hublocker Data Viewer</h1>
        <p>This tool will help you view your current hublocker data from Firebase.</p>
        
        <button onclick="loadPingGoData()">Load PingGo Hublockers</button>
        <button onclick="loadPingGoProducts()">Load PingGo Products</button>
        <button onclick="loadLockerHubData()">Load LockerHub Hublockers</button>
        <button onclick="loadLockerHubProducts()">Load LockerHub Products</button>
        
        <div id="pinggo-section" class="project-section" style="display: none;">
            <div class="project-title">📊 PingGo Project (pinggo-351c6)</div>
            <div id="pinggo-content">
                <div class="loading">Loading...</div>
            </div>
        </div>
        
        <div id="pinggo-products-section" class="project-section" style="display: none;">
            <div class="project-title">🛍️ PingGo Products (pinggo-351c6)</div>
            <div id="pinggo-products-content">
                <div class="loading">Loading...</div>
            </div>
        </div>

        <div id="lockerhub-section" class="project-section" style="display: none;">
            <div class="project-title">📊 LockerHub Hublockers (lockerhub-5382f)</div>
            <div id="lockerhub-content">
                <div class="loading">Loading...</div>
            </div>
        </div>

        <div id="lockerhub-products-section" class="project-section" style="display: none;">
            <div class="project-title">🛍️ LockerHub Products (lockerhub-5382f)</div>
            <div id="lockerhub-products-content">
                <div class="loading">Loading...</div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, getDocs } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase configurations
        const pinggoConfig = {
            apiKey: 'AIzaSyAU3FRDX8Ubgb06lJyLqYOEMTwnHaywT6g',
            authDomain: 'pinggo-351c6.firebaseapp.com',
            projectId: 'pinggo-351c6',
            storageBucket: 'pinggo-351c6.firebasestorage.app',
            messagingSenderId: '41313207774',
            appId: '1:41313207774:web:71d8fbd8c04e504924494f'
        };

        const lockerHubConfig = {
            apiKey: 'AIzaSyBvOHEqIJp6Z9ShlUlYpRwT8TuTkLEABCD',
            appId: '1:502809784341:web:abc123def456ghi789jkl',
            messagingSenderId: '502809784341',
            projectId: 'lockerhub-5382f',
            authDomain: 'lockerhub-5382f.firebaseapp.com',
            storageBucket: 'lockerhub-5382f.appspot.com'
        };

        // Initialize Firebase apps
        const pinggoApp = initializeApp(pinggoConfig, 'pinggo');
        const lockerHubApp = initializeApp(lockerHubConfig, 'lockerhub');

        const pinggoDb = getFirestore(pinggoApp);
        const lockerHubDb = getFirestore(lockerHubApp);

        function displayHublockers(hublockers, containerId) {
            const container = document.getElementById(containerId);

            if (hublockers.length === 0) {
                container.innerHTML = '<div class="error">No hublockers found in this project.</div>';
                return;
            }

            let html = `<div style="margin-bottom: 10px;"><strong>Found ${hublockers.length} hublocker(s):</strong></div>`;

            hublockers.forEach(hublocker => {
                const data = hublocker.data();
                html += `
                    <div class="hublocker-card">
                        <div class="hublocker-name">${data.name || 'Unnamed Hublocker'}</div>
                        <div class="hublocker-details">
                            <strong>ID:</strong> ${hublocker.id}<br>
                            <strong>Address:</strong> ${data.address || 'No address provided'}<br>
                            <strong>City:</strong> ${data.city || 'Unknown'}<br>
                            <strong>District:</strong> ${data.district || 'Unknown'}<br>
                            <strong>Capacity:</strong> ${data.capacity || 'Unknown'}<br>
                            <strong>Available Slots:</strong> ${data.availableSlots || 'Unknown'}<br>
                            <strong>Operating Hours:</strong> ${data.operatingHours || 'Not specified'}<br>
                            <strong>Contact:</strong> ${data.contactPhone || 'No contact info'}<br>
                            <strong>Rating:</strong> ${data.rating || 'No rating'}/5<br>
                            <strong>Status:</strong> <span class="status ${data.isActive ? 'active' : 'inactive'}">${data.isActive ? 'Active' : 'Inactive'}</span>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function displayProducts(products, containerId) {
            const container = document.getElementById(containerId);

            if (products.length === 0) {
                container.innerHTML = '<div class="error">No products found in this project.</div>';
                return;
            }

            let html = `<div style="margin-bottom: 10px;"><strong>Found ${products.length} product(s):</strong></div>`;

            products.forEach(product => {
                const data = product.data();
                const pricing = data.pricing && data.pricing.length > 0 ? data.pricing[0] : {};
                html += `
                    <div class="hublocker-card">
                        <div class="hublocker-name">${data.name || 'Unnamed Product'}</div>
                        <div class="hublocker-details">
                            <strong>ID:</strong> ${product.id}<br>
                            <strong>Description:</strong> ${data.description || 'No description'}<br>
                            <strong>Category:</strong> ${data.category?.name || 'Unknown'}<br>
                            <strong>Owner:</strong> ${data.ownerName || 'Unknown'}<br>
                            <strong>Location:</strong> ${data.location || 'Unknown'}<br>
                            <strong>Hublocker:</strong> ${data.hublockerName || 'Unknown'}<br>
                            <strong>Price:</strong> ${pricing.price || 'Not specified'} ${pricing.currency || ''} per ${pricing.period || 'day'}<br>
                            <strong>Available:</strong> <span class="status ${data.isAvailable ? 'active' : 'inactive'}">${data.isAvailable ? 'Yes' : 'No'}</span><br>
                            <strong>Rating:</strong> ${data.rating || 'No rating'}/5 (${data.reviewCount || 0} reviews)<br>
                            <strong>Quantity:</strong> ${data.availableQuantity || 'Unknown'}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        window.loadPingGoData = async function() {
            const section = document.getElementById('pinggo-section');
            const content = document.getElementById('pinggo-content');
            
            section.style.display = 'block';
            content.innerHTML = '<div class="loading">Loading PingGo data...</div>';
            
            try {
                const querySnapshot = await getDocs(collection(pinggoDb, 'hublockers'));
                const hublockers = [];
                querySnapshot.forEach((doc) => {
                    hublockers.push(doc);
                });
                displayHublockers(hublockers, 'pinggo-content');
            } catch (error) {
                content.innerHTML = `<div class="error">Error loading PingGo data: ${error.message}</div>`;
            }
        };

        window.loadPingGoProducts = async function() {
            const section = document.getElementById('pinggo-products-section');
            const content = document.getElementById('pinggo-products-content');

            section.style.display = 'block';
            content.innerHTML = '<div class="loading">Loading PingGo products...</div>';

            try {
                const querySnapshot = await getDocs(collection(pinggoDb, 'products'));
                const products = [];
                querySnapshot.forEach((doc) => {
                    products.push(doc);
                });
                displayProducts(products, 'pinggo-products-content');
            } catch (error) {
                content.innerHTML = `<div class="error">Error loading PingGo products: ${error.message}</div>`;
            }
        };

        window.loadLockerHubData = async function() {
            const section = document.getElementById('lockerhub-section');
            const content = document.getElementById('lockerhub-content');

            section.style.display = 'block';
            content.innerHTML = '<div class="loading">Loading LockerHub hublockers...</div>';

            try {
                const querySnapshot = await getDocs(collection(lockerHubDb, 'hublockers'));
                const hublockers = [];
                querySnapshot.forEach((doc) => {
                    hublockers.push(doc);
                });
                displayHublockers(hublockers, 'lockerhub-content');
            } catch (error) {
                content.innerHTML = `<div class="error">Error loading LockerHub hublockers: ${error.message}</div>`;
            }
        };

        window.loadLockerHubProducts = async function() {
            const section = document.getElementById('lockerhub-products-section');
            const content = document.getElementById('lockerhub-products-content');

            section.style.display = 'block';
            content.innerHTML = '<div class="loading">Loading LockerHub products...</div>';

            try {
                const querySnapshot = await getDocs(collection(lockerHubDb, 'products'));
                const products = [];
                querySnapshot.forEach((doc) => {
                    products.push(doc);
                });
                displayProducts(products, 'lockerhub-products-content');
            } catch (error) {
                content.innerHTML = `<div class="error">Error loading LockerHub products: ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>
