// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBvOHEqIJp6Z9ShlUlYpRwT8TuTkLEABCD',
    appId: '1:502809784341:web:abc123def456ghi789jkl',
    messagingSenderId: '502809784341',
    projectId: 'lockerhub-5382f',
    authDomain: 'lockerhub-5382f.firebaseapp.com',
    storageBucket: 'lockerhub-5382f.appspot.com',
    measurementId: 'G-XXXXXXXXXX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD5MverSPItyeRJb9oqotC4kPXHTnxCdxg',
    appId: '1:502809784341:android:fa0e249b6949c0af5cde1c',
    messagingSenderId: '502809784341',
    projectId: 'lockerhub-5382f',
    storageBucket: 'lockerhub-5382f.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC7Ow_VbOGGbF8QqY8QqY8QqY8QqY8QqY8',
    appId: '1:123456789:ios:abcdef123456789',
    messagingSenderId: '123456789',
    projectId: 'your-project-id',
    iosBundleId: 'com.example.lockerhub',
    storageBucket: 'your-project-id.appspot.com',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyC7Ow_VbOGGbF8QqY8QqY8QqY8QqY8QqY8',
    appId: '1:123456789:ios:abcdef123456789',
    messagingSenderId: '123456789',
    projectId: 'your-project-id',
    iosBundleId: 'com.example.lockerhub',
    storageBucket: 'your-project-id.appspot.com',
  );
}
